'use client';

import {useState} from 'react';
import {Mail} from 'lucide-react';

export default function Footer() {
  const [email, setEmail] = useState('');

  return (
    <footer className="relative z-10 mt-16">
      <div className="bg-gradient-to-r from-pink-400 to-purple-500 px-4 py-12 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-4 right-8 text-purple-300 text-2xl">💜</div>
        <div className="absolute bottom-6 left-8 text-pink-300 text-xl">⭐</div>
        <div className="absolute top-1/2 left-12 text-yellow-300 text-lg">💎</div>
        <div className="absolute bottom-12 right-16 text-cyan-300 text-lg">🐭</div>

        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-white text-3xl font-bold mb-4">JOIN THE RAVE FAM ✨</h2>
          <p className="text-white/90 text-lg mb-8">get early access to drops + exclusive discounts</p>

          <div className="flex max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1 px-4 py-3 bg-black text-pink-400 placeholder-pink-400/70 border-2 border-gray-400 focus:outline-none focus:border-pink-400"
              style={{borderRadius: 0}}
            />
            <button
              className="px-6 py-3 bg-purple-600 text-white border-2 border-gray-400 hover:bg-purple-700 transition-colors"
              style={{borderRadius: 0}}
            >
              <Mail className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="bg-black px-4 py-12 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-8 left-8 text-pink-500 text-xl">⭐</div>
        <div className="absolute top-16 right-12 text-purple-400 text-2xl">💜</div>
        <div className="absolute bottom-16 left-16 text-yellow-400 text-lg">💎</div>
        <div className="absolute bottom-8 right-8 text-pink-400 text-xl">⭐</div>
        <div className="absolute top-1/2 right-1/4 text-purple-500 text-2xl">●</div>
        <div className="absolute bottom-1/3 left-1/3 text-yellow-400 text-lg">●</div>
        <div className="absolute top-1/3 left-8 text-gray-600 text-4xl">🐭</div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* RAVE RATZ Column */}
            <div>
              <h3 className="text-pink-400 font-bold text-lg mb-4">RAVE RATZ</h3>
              <div className="space-y-2 text-pink-300">
                <p>handmade festival fashion</p>
                <p>ethically sourced</p>
                <p>made with ♡ 4 the community</p>
              </div>
            </div>

            {/* SHOP Column */}
            <div>
              <h3 className="text-pink-400 font-bold text-lg mb-4">SHOP</h3>
              <div className="space-y-2 text-pink-300">
                <p>baby tees</p>
                <p>braids</p>
                <p>leg warmers</p>
                <p>nails</p>
                <p>press-ons</p>
              </div>
            </div>

            {/* INFO Column */}
            <div>
              <h3 className="text-pink-400 font-bold text-lg mb-4">INFO</h3>
              <div className="space-y-2 text-pink-300">
                <p>size guide</p>
                <p>shipping</p>
                <p>returns</p>
                <p>contact</p>
              </div>
            </div>

            {/* CONNECT Column */}
            <div>
              <h3 className="text-pink-400 font-bold text-lg mb-4">CONNECT</h3>
              <div className="space-y-2 text-pink-300">
                <p>instagram</p>
                <p>tiktok</p>
                <p>discord</p>
                <p>spotify</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-black border-t border-gray-800 px-4 py-4">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-pink-400 text-sm">© 2025 RAVE RATZ • made with ♡ for the underground</p>
        </div>
      </div>
    </footer>
  );
}
