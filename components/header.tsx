'use client';

import {useState} from 'react';
import {Search, Mail, Heart, ShoppingCart} from 'lucide-react';

export default function Header() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <header className="relative z-20 p-4">
      <div className="flex items-center justify-between flex-wrap gap-4 mb-4">
        <div className="browser-window" style={{backgroundColor: '#f9a8d4'}}>
          <div className="browser-content p-3">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">🐭</span>
              </div>
              <span className="text-purple-600 font-bold text-xl">RAVE RATZ</span>
            </div>
          </div>
        </div>

        <div className="flex-1 max-w-md mx-4">
          <div className="relative">
            <input
              type="text"
              placeholder="search 4 ur vibe..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input w-full px-4 py-2 text-purple-800 placeholder-purple-400"
            />
            <Search className="absolute right-3 top-2.5 w-5 h-5 text-purple-400" />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            className="bg-pink-500 text-white px-4 py-2 text-sm font-bold hover:bg-pink-600 transition-colors border-2 border-gray-400"
            style={{borderRadius: 0}}
          >
            <Mail className="w-4 h-4 inline mr-1" />
            Join List
          </button>
          <button className="relative p-2 text-purple-600 hover:text-pink-500 transition-colors">
            <Heart className="w-6 h-6" />
            <span
              className="absolute -top-1 -right-1 bg-cyan-400 text-black text-xs w-5 h-5 flex items-center justify-center font-bold"
              style={{borderRadius: '50%'}}
            >
              7
            </span>
          </button>
          <button className="relative p-2 text-purple-600 hover:text-pink-500 transition-colors">
            <ShoppingCart className="w-6 h-6" />
            <span
              className="absolute -top-1 -right-1 bg-yellow-400 text-black text-xs w-5 h-5 flex items-center justify-center font-bold"
              style={{borderRadius: '50%'}}
            >
              3
            </span>
          </button>
        </div>
      </div>

      <div className="flex justify-center">
        <div className="flex flex-wrap gap-2 justify-center">
          {['home', 'baby tees', 'braids', 'leg warmers', 'press-on nails', 'custom fits'].map((item) => (
            <button
              key={item}
              className="nav-button px-4 py-2 font-medium transition-colors"
              style={{backgroundColor: '#d8b4fe'}}
            >
              {item}
            </button>
          ))}
        </div>
      </div>
    </header>
  );
}
