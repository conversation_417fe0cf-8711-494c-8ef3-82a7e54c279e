import {getProducts, ShopifyProduct} from '@/lib/shopify';
import ProductGridClient from './product-grid-client';

// Helper function to extract product type/category from Shopify data
function getProductCategory(product: ShopifyProduct): string {
  // Try to get category from product type first
  if (product.productType && product.productType.trim()) {
    return product.productType.toLowerCase().trim();
  }

  // Fallback to tags if product type is not set
  const categoryTags = ['baby tees', 'braids', 'leg warmers', 'nails', 'press-ons', 'kandi', 'jewelry', 'accessories'];
  for (const tag of product.tags) {
    const lowerTag = tag.toLowerCase();
    if (categoryTags.includes(lowerTag)) {
      return lowerTag;
    }
  }

  // Try to infer from product title
  const title = product.title.toLowerCase();
  if (title.includes('kandi')) return 'kandi';
  if (title.includes('tee') || title.includes('shirt')) return 'baby tees';
  if (title.includes('braid')) return 'braids';
  if (title.includes('nail')) return 'nails';
  if (title.includes('leg warmer')) return 'leg warmers';

  // Default category
  return 'accessories';
}

// Helper function to check if product is limited edition
function isLimitedEdition(product: ShopifyProduct): boolean {
  return product.tags.some((tag) =>
    tag.toLowerCase().includes('limited')
    || tag.toLowerCase().includes('exclusive')
    || tag.toLowerCase().includes('rare'),
  );
}

// Transform Shopify product to our component format
function transformShopifyProduct(product: ShopifyProduct) {
  return {
    id: product.id,
    shopifyId: product.id,
    name: product.title.toLowerCase(),
    category: getProductCategory(product),
    price: parseFloat(product.priceRange.minVariantPrice.amount),
    image: product.featuredImage?.url || '/placeholder.svg',
    limited: isLimitedEdition(product),
    favorited: false, // Will be managed by client component
    handle: product.handle,
    description: product.description,
    tags: product.tags,
    productType: product.productType,
    vendor: product.vendor,
    variants: product.variants,
    priceRange: product.priceRange,
  };
}

export default async function ProductGrid() {
  try {
    // Fetch products from Shopify
    const productsData = await getProducts(12); // Fetch up to 12 products

    // Transform Shopify products to our component format
    const products = productsData?.edges?.map((edge: {node: ShopifyProduct}) =>
      transformShopifyProduct(edge.node),
    ) || [];

    // Extract unique categories from products
    const productCategories = Array.from(
      new Set(products.map((product: {category: string}) => product.category)),
    ).sort();

    const categories = ['all', ...productCategories];

    // If no products found, show a message
    if (products.length === 0) {
      return (
        <section className="relative z-10 px-4 py-8">
          <div className="max-w-7xl mx-auto text-center">
            <div className="browser-window">
              <div className="browser-content p-8">
                <h2 className="text-2xl font-bold text-purple-600 mb-4">No Products Found</h2>
                <p className="text-gray-700">
                  Add some products to your Shopify store to see them here!
                </p>
              </div>
            </div>
          </div>
        </section>
      );
    }

    // Pass data to client component for interactivity
    return <ProductGridClient products={products} categories={categories as string[]} />;
  } catch (error) {
    console.error('Error fetching products:', error);

    // Show error state
    return (
      <section className="relative z-10 px-4 py-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="browser-window">
            <div className="browser-content p-8">
              <h2 className="text-2xl font-bold text-red-600 mb-4">Error Loading Products</h2>
              <p className="text-gray-700 mb-4">
                Unable to fetch products from Shopify. Please try again later.
              </p>
              <p className="text-sm text-gray-500">
                Error:
                {' '}
                {error instanceof Error ? error.message : 'Unknown error'}
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }
}
