'use client';

import {ShopifyProvider, CartProvider} from '@shopify/hydrogen-react';
import {ReactNode} from 'react';
import {env} from '@/lib/env';

type ShopifyProvidersProps = {
  children: ReactNode;
};

export default function ShopifyProviders({children}: ShopifyProvidersProps) {
  return (
    <ShopifyProvider
      storeDomain={env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN}
      storefrontToken={env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN}
      storefrontApiVersion={env.NEXT_PUBLIC_SHOPIFY_API_VERSION}
      countryIsoCode="US"
      languageIsoCode="EN"
    >
      <CartProvider>
        {children}
      </CartProvider>
    </ShopifyProvider>
  );
}
