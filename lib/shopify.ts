import {createStorefrontApiClient} from '@shopify/storefront-api-client';
import {env, getShopifyStoreUrl} from './env';

// Create the Shopify Storefront API client
export const storefrontClient = createStorefrontApiClient({
  storeDomain: getShopifyStoreUrl(),
  apiVersion: env.NEXT_PUBLIC_SHOPIFY_API_VERSION,
  publicAccessToken: env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN,
});

// GraphQL queries for common operations
export const PRODUCTS_QUERY = `#graphql
  query Products($first: Int!) {
    products(first: $first) {
      edges {
        node {
          id
          title
          handle
          description
          featuredImage {
            url
            altText
            width
            height
          }
          priceRange {
            minVariantPrice {
              amount
              currencyCode
            }
            maxVariantPrice {
              amount
              currencyCode
            }
          }
          variants(first: 1) {
            edges {
              node {
                id
                title
                price {
                  amount
                  currencyCode
                }
                availableForSale
                selectedOptions {
                  name
                  value
                }
              }
            }
          }
          tags
          productType
          vendor
          createdAt
          updatedAt
          options {
            id
            name
            values
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

export const PRODUCT_BY_HANDLE_QUERY = `#graphql
  query ProductByHandle($handle: String!) {
    product(handle: $handle) {
      id
      title
      handle
      description
      descriptionHtml
      featuredImage {
        url
        altText
        width
        height
      }
      images(first: 10) {
        edges {
          node {
            url
            altText
            width
            height
          }
        }
      }
      priceRange {
        minVariantPrice {
          amount
          currencyCode
        }
        maxVariantPrice {
          amount
          currencyCode
        }
      }
      variants(first: 100) {
        edges {
          node {
            id
            title
            price {
              amount
              currencyCode
            }
            compareAtPrice {
              amount
              currencyCode
            }
            availableForSale
            selectedOptions {
              name
              value
            }
            image {
              url
              altText
              width
              height
            }
          }
        }
      }
      options {
        id
        name
        values
      }
      tags
      productType
      vendor
      seo {
        title
        description
      }
      createdAt
      updatedAt
    }
  }
`;

export const COLLECTIONS_QUERY = `#graphql
  query Collections($first: Int!) {
    collections(first: $first) {
      edges {
        node {
          id
          title
          handle
          description
          image {
            url
            altText
            width
            height
          }
          products(first: 10) {
            edges {
              node {
                id
                title
                handle
                featuredImage {
                  url
                  altText
                  width
                  height
                }
                priceRange {
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
    }
  }
`;

export const CART_CREATE_MUTATION = `#graphql
  mutation CartCreate($input: CartInput!) {
    cartCreate(input: $input) {
      cart {
        id
        checkoutUrl
        totalQuantity
        cost {
          totalAmount {
            amount
            currencyCode
          }
          subtotalAmount {
            amount
            currencyCode
          }
          totalTaxAmount {
            amount
            currencyCode
          }
          totalDutyAmount {
            amount
            currencyCode
          }
        }
        lines(first: 100) {
          edges {
            node {
              id
              quantity
              cost {
                totalAmount {
                  amount
                  currencyCode
                }
              }
              merchandise {
                ... on ProductVariant {
                  id
                  title
                  price {
                    amount
                    currencyCode
                  }
                  product {
                    id
                    title
                    handle
                    featuredImage {
                      url
                      altText
                      width
                      height
                    }
                  }
                }
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_LINES_ADD_MUTATION = `#graphql
  mutation CartLinesAdd($cartId: ID!, $lines: [CartLineInput!]!) {
    cartLinesAdd(cartId: $cartId, lines: $lines) {
      cart {
        id
        checkoutUrl
        totalQuantity
        cost {
          totalAmount {
            amount
            currencyCode
          }
          subtotalAmount {
            amount
            currencyCode
          }
          totalTaxAmount {
            amount
            currencyCode
          }
          totalDutyAmount {
            amount
            currencyCode
          }
        }
        lines(first: 100) {
          edges {
            node {
              id
              quantity
              cost {
                totalAmount {
                  amount
                  currencyCode
                }
              }
              merchandise {
                ... on ProductVariant {
                  id
                  title
                  price {
                    amount
                    currencyCode
                  }
                  product {
                    id
                    title
                    handle
                    featuredImage {
                      url
                      altText
                      width
                      height
                    }
                  }
                }
              }
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// Helper functions for API calls
export async function getProducts(first = 10) {
  try {
    const {data, errors} = await storefrontClient.request(PRODUCTS_QUERY, {
      variables: {first},
    });

    if (errors) {
      console.error('GraphQL errors:', errors);
      throw new Error('Failed to fetch products');
    }

    return data?.products;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

export async function getProductByHandle(handle: string) {
  try {
    const {data, errors} = await storefrontClient.request(PRODUCT_BY_HANDLE_QUERY, {
      variables: {handle},
    });

    if (errors) {
      console.error('GraphQL errors:', errors);
      throw new Error(`Failed to fetch product with handle: ${handle}`);
    }

    return data?.product;
  } catch (error) {
    console.error('Error fetching product:', error);
    throw error;
  }
}

export async function getCollections(first = 10) {
  try {
    const {data, errors} = await storefrontClient.request(COLLECTIONS_QUERY, {
      variables: {first},
    });

    if (errors) {
      console.error('GraphQL errors:', errors);
      throw new Error('Failed to fetch collections');
    }

    return data?.collections;
  } catch (error) {
    console.error('Error fetching collections:', error);
    throw error;
  }
}

export async function createCart(lines: {
  merchandiseId: string;
  quantity: number;
}[] = []) {
  try {
    const {data, errors} = await storefrontClient.request(CART_CREATE_MUTATION, {
      variables: {
        input: {
          lines,
        },
      },
    });

    if (errors) {
      console.error('GraphQL errors:', errors);
      throw new Error('Failed to create cart');
    }

    if (data?.cartCreate?.userErrors?.length > 0) {
      console.error('Cart creation errors:', data.cartCreate.userErrors);
      throw new Error('Failed to create cart: ' + data.cartCreate.userErrors[0].message);
    }

    return data?.cartCreate?.cart;
  } catch (error) {
    console.error('Error creating cart:', error);
    throw error;
  }
}

export async function addToCart(cartId: string, lines: {
  merchandiseId: string;
  quantity: number;
}[]) {
  try {
    const {data, errors} = await storefrontClient.request(CART_LINES_ADD_MUTATION, {
      variables: {
        cartId,
        lines,
      },
    });

    if (errors) {
      console.error('GraphQL errors:', errors);
      throw new Error('Failed to add items to cart');
    }

    if (data?.cartLinesAdd?.userErrors?.length > 0) {
      console.error('Add to cart errors:', data.cartLinesAdd.userErrors);
      throw new Error('Failed to add to cart: ' + data.cartLinesAdd.userErrors[0].message);
    }

    return data?.cartLinesAdd?.cart;
  } catch (error) {
    console.error('Error adding to cart:', error);
    throw error;
  }
}

// Type definitions for better TypeScript support
export type ShopifyProduct = {
  id: string;
  title: string;
  handle: string;
  description: string;
  featuredImage?: {
    url: string;
    altText?: string;
    width?: number;
    height?: number;
  };
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  variants: {
    edges: {
      node: {
        id: string;
        title: string;
        price: {
          amount: string;
          currencyCode: string;
        };
        availableForSale: boolean;
      };
    }[];
  };
  tags: string[];
  productType: string;
  vendor: string;
};

export type ShopifyCollection = {
  id: string;
  title: string;
  handle: string;
  description: string;
  image?: {
    url: string;
    altText?: string;
    width?: number;
    height?: number;
  };
  products: {
    edges: {
      node: ShopifyProduct;
    }[];
  };
};

export type ShopifyCart = {
  id: string;
  checkoutUrl: string;
  totalQuantity: number;
  cost: {
    totalAmount: {
      amount: string;
      currencyCode: string;
    };
    subtotalAmount: {
      amount: string;
      currencyCode: string;
    };
  };
  lines: {
    edges: {
      node: {
        id: string;
        quantity: number;
        cost: {
          totalAmount: {
            amount: string;
            currencyCode: string;
          };
        };
        merchandise: {
          id: string;
          title: string;
          price: {
            amount: string;
            currencyCode: string;
          };
          product: {
            id: string;
            title: string;
            handle: string;
            featuredImage?: {
              url: string;
              altText?: string;
              width?: number;
              height?: number;
            };
          };
        };
      };
    }[];
  };
};
