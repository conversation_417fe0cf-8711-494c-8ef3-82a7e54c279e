import {z} from 'zod';

// Client-side environment variables (NEXT_PUBLIC_* - accessible in browser)
const clientEnvSchema = z.object({
  NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN: z
    .string()
    .min(1, 'Shopify store domain is required')
    .refine(
      (domain) => domain.includes('.myshopify.com') || domain.includes('.'),
      'Store domain must be a valid Shopify domain',
    ),

  NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN: z
    .string()
    .min(1, 'Shopify Storefront access token is required'),

  NEXT_PUBLIC_SHOPIFY_API_VERSION: z
    .string()
    .min(1, 'Shopify API version is required')
    .regex(/^\d{4}-\d{2}$/, 'API version must be in format YYYY-MM (e.g., 2025-07)'),
});

// Server-side only environment variables (no NEXT_PUBLIC_* prefix)
const serverEnvSchema = z.object({
  SHOPIFY_ADMIN_ACCESS_TOKEN: z
    .string()
    .min(1, 'Shopify Admin access token is required')
    .startsWith('shpat_', 'Admin access token must start with "shpat_"'),
});

export type ClientEnv = z.infer<typeof clientEnvSchema>;
export type ServerEnv = z.infer<typeof serverEnvSchema>;

function parseClientEnv(): ClientEnv {
  const parsed = clientEnvSchema.safeParse({
    NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN: process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN,
    NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN:
    process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN,
    NEXT_PUBLIC_SHOPIFY_API_VERSION: process.env.NEXT_PUBLIC_SHOPIFY_API_VERSION,
  });

  if (!parsed.success) {
    const errors = parsed.error.issues
      .map((issue) => `  - ${issue.path.join('.')}: ${issue.message}`)
      .join('\n');

    const message = `❌ Invalid environment variables:\n${errors}`;
    console.error(message);
    throw new Error(message);
  }

  return parsed.data;
}

function parseServerEnv(): ServerEnv {
  if (typeof window !== 'undefined') {
    throw new Error('Server environment variables cannot be accessed on the client side');
  }

  const parsed = serverEnvSchema.safeParse({
    SHOPIFY_ADMIN_ACCESS_TOKEN: process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
  });

  if (!parsed.success) {
    const errors = parsed.error.issues
      .map((issue) => `  - ${issue.path.join('.')}: ${issue.message}`)
      .join('\n');

    const message = `❌ Invalid server environment variables:\n${errors}`;
    console.error(message);
    throw new Error(message);
  }

  return parsed.data;
}

// Client environment variables - safe to use anywhere
export const env = parseClientEnv();

// Server environment variables - only use on server side (API routes, Server Components, etc.)
export const serverEnv = typeof window === 'undefined' ? parseServerEnv() : ({} as ServerEnv);

// Helper to get full Shopify store URL
export function getShopifyStoreUrl(): string {
  const domain = env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN;

  if (domain.includes('.myshopify.com')) {
    return `https://${domain}`;
  }

  const storeName = domain.replace(/\.(space|com|net|org)$/, '');
  return `https://${storeName}.myshopify.com`;
}
