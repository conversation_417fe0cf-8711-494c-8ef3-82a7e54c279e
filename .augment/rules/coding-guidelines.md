---
type: "always_apply"
---

# Coding Guidelines

## DO

- **Use Server Components by default** - Only use Client Components when necessary (interactivity, hooks)
- **Use Shopify Storefront GraphQL API** - All product/commerce data comes from Shopify
- **Use CSS Modules** - Component-scoped styles, avoid global CSS
- **Use TypeScript strict mode** - Type everything, no `any` types
- **Use Next.js Image component** - For all images with proper sizing
- **Use semantic HTML** - `<nav>`, `<main>`, `<article>`, etc.
- **Colocate component files** - Keep components with their styles
- **Follow existing patterns** - Match the codebase conventions
- **Keep dependencies lean** - Only add packages when necessary
- **Provide file paths** - Always specify exact file locations in responses

## DO NOT

- **DO NOT use Tailwind, Bootstrap, or CSS frameworks** - Use vanilla CSS and CSS Modules only
- **DO NOT use Redux, Zustand, or global state libraries** - Use React hooks and minimal client state
- **DO NOT create custom backend APIs** - Shopify Storefront GraphQL API only
- **DO NOT add unnecessary dependencies** - Keep the bundle size small
- **DO NOT use client-side data fetching** - Except for cart mutations and real-time interactions
- **DO NOT write markdown documentation** - Unless explicitly asked, only implement code
- **DO NOT start the dev server** - User will test manually (provide test guidance instead)
- **DO NOT over-engineer** - Prefer simple solutions over complex ones
- **DO NOT invent features** - Only build what's specified in project guidelines

## Code Style

- Include brief comments only for complex logic
- Prioritize readability and maintainability
- Keep responses concise and actionable
- Use clear, descriptive names for files and components
