---
type: "agent_requested"
description: "The high-level project guidelines and product goals to keep in mind when building features for Rave Ratz app"
---

# Rave <PERSON>z Project Guidelines

## Project Overview

**Rave <PERSON>** is an ecommerce storefront for custom, made-to-order festival fashion and beauty accessories. The platform serves the EDM/festival community with handmade, ethically sourced products through limited-time collection "drops."

### Brand Identity
- **Ethos**: Handmade, ethically sourced, inclusive, EDM-community forward
- **Aesthetic**: MySpace-era maximalist, playful "ugly-cute" vibe with 90s-2000s lofi nostalgia
- **Visual Elements**: Tiled 3D dancing baby backgrounds, stickers/emojis, vibrant gradients, nostalgic web design patterns
- **Voice**: Inclusive, gender-neutral, enthusiastic, community-focused

### Product Categories
- Baby tees
- Braids (hair accessories)
- Leg warmers
- Nails (press-on nails and nail art)
- Other festival fashion accessories

---

## Core Objectives

### Primary User Capabilities
Users must be able to:
1. Browse products by category
2. View detailed product information (images, pricing, materials, customization options)
3. Add items to cart and manage cart contents
4. Add products to favorites/wishlist
5. Complete purchases through checkout
6. Join email list for drop announcements
7. Create accounts and view order history

### Business Requirements
1. Support scheduled, limited-time collection "drops" with start/end times
2. Feature active drops prominently on landing page
3. Capture email signups for marketing and drop notifications
4. Track key ecommerce events for analytics

---

## Target Audience

### Demographics
- **Age Range**: 18-40 years old
- **Community**: EDM/rave/festival attendees and enthusiasts
- **Values**: Inclusivity, self-expression, sustainability, community

### UX Requirements
- **Gender-neutral**: All copy, imagery, and UX patterns must be non-gendered and inclusive
- **Accessibility**: WCAG 2.1 AA compliance is mandatory
- **Mobile-first**: Primary experience optimized for mobile devices

---

## Success Criteria

### User Experience
- Smooth, intuitive navigation and interactions
- Mobile-first responsive design
- Vibrant maximalist aesthetic that maintains usability
- WCAG 2.1 AA color contrast compliance despite bold visual design
- Fast page loads and smooth transitions

### Business Outcomes
- High conversion rates for drop collections
- Growing email subscriber base
- Strong customer retention and repeat purchases
- Positive brand perception in the EDM/festival community

---

## Application Structure

### Key Pages and User Flows

#### Public Pages
- **Landing Page**: Hero section, featured collections, active drops, email signup CTA
- **Category Pages**: Product grid filtered by category
- **Product Detail Pages**: Gallery, pricing, options, add-to-cart, favorite toggle, materials/ethics info
- **Cart**: Cart management, quantity updates, remove items, proceed to checkout
- **Checkout**: Complete purchase flow

#### Authenticated Pages
- **Account Dashboard**: Order history, account settings
- **Sign In/Sign Up**: User authentication
- **Favorites/Wishlist**: Saved products

#### Supporting Pages
- **Search Results**: Product search functionality
- **Order Confirmation**: Post-purchase confirmation

### Essential Components
- **Header/Navigation**: Logo, category links, search, account icon, cart icon with count
- **Drop Banner**: Prominent CTA for active limited-time collections
- **Footer**: Policies (privacy, returns, shipping), social links, contact info
- **Email Signup**: Inline and modal variants for list capture
- **Product Card**: Product thumbnail, title, price, quick-add, favorite toggle
- **Product Gallery**: Image carousel/gallery for product detail pages
- **Add to Cart**: Variant selection, quantity picker, add-to-cart button

---

## Feature: Limited-Time Drops

### Business Purpose
Drops create urgency and exclusivity, driving engagement and sales for limited-edition collections.

### User Experience
- Clear visual distinction for drop collections
- Countdown timer showing time remaining
- Email signup CTA for drop notifications
- "Notify me" option for upcoming drops
- Badge or hide ended drops appropriately
- Support "sold out" state when inventory depletes

---

## Content and Copy Guidelines

### Language Requirements
- **Inclusive and gender-neutral**: Avoid gendered terms (e.g., use "they/them," "folks," "everyone")
- **Brand voice**: Enthusiastic, playful, community-oriented, authentic
- **Product descriptions**: Emphasize handmade quality, ethical sourcing, customization options
- **Accessibility**: Clear, concise microcopy for screen readers and UI labels

### Messaging Priorities
1. Handmade and custom-made quality
2. Ethical sourcing and sustainable practices
3. Community and inclusivity
4. Festival/rave culture celebration

---

## Quality Standards

### User Experience
- Mobile-first, accessible, performant
- WCAG 2.1 AA compliance for all pages
- Fast page loads (LCP < 2.5s on mobile)
- Smooth interactions with minimal layout shift

### Technical Quality
- Production-ready code (builds successfully, no type errors)
- Clean, maintainable component structure
- Optimized images and assets
- SEO-friendly with proper metadata

---

## Summary

When building features for Rave Ratz, always prioritize:
1. **User experience**: Mobile-first, accessible, performant
2. **Brand alignment**: Maximalist aesthetic with inclusive, community-focused messaging
3. **Business goals**: Drive conversions, grow email list, support drop model
4. **Quality**: Clean code, maintainable patterns, production-ready implementation
5. **Inclusivity**: Gender-neutral language and WCAG AA accessibility compliance
