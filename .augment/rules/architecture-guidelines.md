---
type: "agent_requested"
description: "The technical architecture patterns, technology stack decisions, and quality standards that govern how <PERSON><PERSON> is built"
---

# <PERSON>ve <PERSON> Architecture Guidelines

## Technology Stack

- **Framework**: Next.js 15+ with App Router
- **Commerce Backend**: Shopify Storefront GraphQL API (exclusive data source)
- **Styling**: Vanilla CSS for globals, CSS Modules for component-scoped styles
- **State Management**: Minimal client state (React hooks)
- **Deployment**: Vercel
- **Language**: TypeScript with strict mode

---

## Core Architectural Patterns

### Server-First Architecture
The application follows a server-first approach to maximize performance and SEO:
- **Server Components** are the default for all pages and non-interactive UI
- **Client Components** are used sparingly for interactivity (cart, favorites, search, modals)
- **Server Actions** handle mutations and form submissions
- **Streaming SSR** with Suspense boundaries for progressive rendering

### Data Layer
All commerce data flows through Shopify's Storefront GraphQL API:
- Product and collection data fetched server-side
- Cart operations use Shopify Cart API
- No custom backend or database
- Client-side fetching only for real-time interactions (cart updates, wishlist toggles)

### Routing Strategy
File-based routing under `/app` directory:
- Dynamic routes use `[handle]` pattern for products and categories
- Route groups organize related pages
- Parallel routes for modals and overlays

### Styling Architecture
Component-scoped styling with minimal global CSS:
- CSS Modules for component styles
- Global CSS limited to resets, CSS variables, and brand tokens
- No CSS frameworks (Tailwind, Bootstrap, etc.)
- Reusable components over shared CSS classes

### State Management Philosophy
Minimal client state with clear persistence strategies:
- **Cart**: Shopify Cart API (preferred) or localStorage
- **Favorites**: localStorage or Shopify customer metafields
- **UI State**: Local component state with React hooks
- No global state libraries (Redux, Zustand, etc.)

---

## Quality Standards

### Accessibility (WCAG 2.1 AA)
- Semantic HTML5 elements
- Keyboard navigation for all interactive elements
- Focus management for modals and dynamic content
- ARIA labels where semantic HTML is insufficient
- Color contrast ratios: 4.5:1 (normal text), 3:1 (large text)

### Performance Targets
- **LCP**: < 2.5s on mobile
- **CLS**: < 0.1
- **FID/INP**: < 100ms
- Optimized images via Next.js Image component
- Code splitting and dynamic imports
- Minimal client-side JavaScript

### SEO Requirements
- Dynamic metadata for all pages
- Open Graph tags for social sharing
- Structured data (JSON-LD): Product, Breadcrumb, Collection schemas
- Canonical URLs
- XML sitemap

---

## File Organization

Follow Next.js App Router conventions with clear separation of concerns:

```
/app                    # Next.js App Router pages
  /(routes)             # Route groups
/components             # React components
  /ui                   # Reusable UI components
  /cart                 # Cart-specific components
  /product              # Product-specific components
/lib                    # Utilities and helpers
  /shopify              # Shopify API integration
  /utils                # General utilities
/styles                 # Global styles
```

**Principles**:
- Colocate components with their styles
- Separate data fetching from UI components
- Use clear, descriptive file and component names

---

## Development Approach

### Decision-Making
- Prefer simpler solutions over complex ones
- Ask for clarification when requirements are ambiguous
- Explain architectural decisions briefly
- Highlight trade-offs when multiple approaches are viable

### Code Quality
- TypeScript strict mode with no `any` types
- Build must succeed without errors
- Pages must render successfully on server
- Pass basic accessibility audits (axe, Lighthouse)

---

## Constraints

### What NOT to Do
- Do not create custom backend APIs beyond Shopify integration
- Do not add CSS frameworks (Tailwind, Bootstrap, etc.)
- Do not add global state libraries (Redux, Zustand, etc.)
- Do not over-engineer solutions
- Do not invent features beyond project guidelines
- Do not add unnecessary dependencies
