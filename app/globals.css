@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  /* optional: --font-sans, --font-serif, --font-mono if they are applied in the layout.tsx */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Y2K Grid Background */
body {
  background: linear-gradient(135deg, #ff1493 0%, #9932cc 50%, #4b0082 100%);
  background-attachment: fixed;
  position: relative;
  overflow-x: hidden;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

/* Floating decorative elements */
.floating-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
  top: 10%;
  left: 5%;
  animation-delay: 0s;
}
.floating-element:nth-child(2) {
  top: 20%;
  right: 10%;
  animation-delay: 1s;
}
.floating-element:nth-child(3) {
  top: 60%;
  left: 15%;
  animation-delay: 2s;
}
.floating-element:nth-child(4) {
  bottom: 20%;
  right: 20%;
  animation-delay: 3s;
}
.floating-element:nth-child(5) {
  top: 40%;
  right: 5%;
  animation-delay: 4s;
}
.floating-element:nth-child(6) {
  bottom: 10%;
  left: 10%;
  animation-delay: 5s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Browser window styling */
.browser-window {
  background: rgba(255, 192, 203, 0.9); /* Light pink background instead of white */
  border: 2px solid #808080; /* Gray border instead of pink */
  border-radius: 0; /* Remove border radius for sharp corners */
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* Windows-style shadow */
  position: relative;
  overflow: hidden;
}

/* Remove window controls from regular browser windows */
.browser-window::before,
.browser-window::after {
  display: none;
}

/* New class for product cards that keep window controls */
.product-window {
  background: rgba(255, 192, 203, 0.9);
  border: 2px solid #808080;
  border-radius: 0;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.product-window::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(90deg, #c0c0c0, #e0e0e0);
  border-bottom: 1px solid #808080;
}

.product-window::after {
  content: "⚫ ⚫ ⚫";
  position: absolute;
  top: 4px;
  right: 6px;
  color: #404040;
  font-size: 6px;
  z-index: 10;
}

.browser-content {
  padding-top: 0; /* Remove top padding since no window controls */
  position: relative;
  z-index: 5;
}

.product-content {
  padding-top: 25px; /* Add padding for product windows with controls */
  position: relative;
  z-index: 5;
}

/* Updated navigation button styling */
.nav-button {
  background: rgba(230, 230, 250, 0.9); /* Light lavender background */
  border: 2px solid #808080; /* Gray border */
  border-radius: 0; /* Sharp corners */
  color: #4b0082; /* Dark purple text */
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.nav-button:hover {
  background: rgba(255, 182, 193, 0.9); /* Light pink on hover */
  border-color: #606060;
}

/* Updated search bar styling */
.search-input {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid #808080;
  border-radius: 0;
  color: #4b0082;
}

.search-input:focus {
  border-color: #606060;
  outline: none;
}

/* Glitch text effect */
.glitch {
  position: relative;
  color: #ff1493;
  font-weight: bold;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #00ffff;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #ffff00;
  z-index: -2;
}

@keyframes glitch-1 {
  0%,
  14%,
  15%,
  49%,
  50%,
  99%,
  100% {
    transform: translate(0);
  }
  15%,
  49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%,
  20%,
  21%,
  62%,
  63%,
  99%,
  100% {
    transform: translate(0);
  }
  21%,
  62% {
    transform: translate(2px, -2px);
  }
}

/* Neon glow effects */
.neon-glow {
  text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor, 0 0 20px currentColor;
}

.neon-border {
  box-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor, inset 0 0 5px currentColor;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #1a0033;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #ff1493, #9932cc);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #ff69b4, #ba55d3);
}
