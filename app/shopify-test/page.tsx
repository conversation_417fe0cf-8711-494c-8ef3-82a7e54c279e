'use client';

import {getProducts, ShopifyProduct} from '@/lib/shopify';
import Image from 'next/image';
import {useEffect, useState} from 'react';

export default function ShopifyTestPage() {
  const [products, setProducts] = useState<ShopifyProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProducts() {
      try {
        setLoading(true);
        const productsData = await getProducts(6);
        setProducts(productsData?.edges?.map((edge: {node: ShopifyProduct}) => edge.node) || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch products');
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchProducts();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto"></div>
          <p className="mt-4 text-lg">Loading products from Shopify...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">❌ Error</div>
          <p className="text-lg">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-pink-500">
          Shopify Products Test
        </h1>

        <div className="mb-6 text-center">
          <p className="text-lg">
            ✅ Successfully connected to Shopify! Found
            {' '}
            {products.length}
            {' '}
            products.
          </p>
        </div>

        {products.length === 0
          ? (
              <div className="text-center">
                <p className="text-lg">No products found in your Shopify store.</p>
                <p className="text-sm text-gray-600 mt-2">
                  Add some products to your Shopify admin to see them here.
                </p>
              </div>
            )
          : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product) => (
                  <div
                    key={product.id}
                    className="bg-white border-2 border-gray-600 rounded-none shadow-lg overflow-hidden"
                  >
                    {product.featuredImage && (
                      <Image
                        src={product.featuredImage.url}
                        alt={product.featuredImage.altText || product.title}
                        width={800}
                        height={400}
                        className="w-full h-48 object-cover"
                      />
                    )}

                    <div className="p-4">
                      <h3 className="text-lg font-bold text-purple-600 mb-2">
                        {product.title}
                      </h3>

                      {product.description && (
                        <p className="text-gray-700 text-sm mb-3 line-clamp-3">
                          {product.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-pink-500">
                          $
                          {product.priceRange.minVariantPrice.amount}
                          {' '}
                          {product.priceRange.minVariantPrice.currencyCode}
                        </div>

                        <div className="text-sm text-gray-600">
                          {product.productType}
                        </div>
                      </div>

                      {product.tags.length > 0 && (
                        <div className="mt-3 flex flex-wrap gap-1">
                          {product.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="bg-purple-100 text-purple-800 text-xs px-2 py-1 border border-purple-300"
                            >
                              {tag}
                            </span>
                          ))}
                          {product.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +
                              {product.tags.length - 3}
                              {' '}
                              more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold mb-4">Next Steps</h2>
          <div className="text-left max-w-2xl mx-auto space-y-2">
            <p>✅ Shopify Storefront API is configured and working</p>
            <p>✅ Environment variables are set up</p>
            <p>✅ GraphQL queries are ready to use</p>
            <p>🔧 You can now integrate these products into your main product grid</p>
            <p>🔧 Add cart functionality using the provided cart mutations</p>
            <p>🔧 Customize the product display to match your Y2K theme</p>
          </div>
        </div>
      </div>
    </div>
  );
}
