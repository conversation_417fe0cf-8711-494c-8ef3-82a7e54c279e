import type React from 'react';
import type {Metadata} from 'next';
import './globals.css';
import ShopifyProviders from '@/components/shopify-provider';

export const metadata: Metadata = {
  title: 'RAVE RATZ - Handmade Festival Fashion',
  description:
    'Custom pieces • ethically sourced • made with love - Underground festival fashion for the EDM community',
  generator: 'v0.app',
  keywords: ['rave fashion', 'festival clothing', 'EDM fashion', 'handmade', 'custom pieces', 'ethically sourced'],
  authors: [{name: '<PERSON>'}],
  creator: 'RAVE RATZ',
  publisher: 'RAVE RATZ',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://raveratz.space'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'RAVE RATZ - Handmade Festival Fashion',
    description: 'Custom pieces • ethically sourced • made with love - Underground festival fashion for the EDM community',
    url: 'https://raveratz.space',
    siteName: 'RAVE RATZ',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RAVE RATZ - Handmade Festival Fashion',
    description: 'Custom pieces • ethically sourced • made with love - Underground festival fashion for the EDM community',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      'index': true,
      'follow': true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      {url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png'},
      {url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png'},
      {url: '/favicon.ico', sizes: 'any'},
    ],
    apple: [
      {url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png'},
    ],
    other: [
      {
        rel: 'android-chrome-192x192',
        url: '/android-chrome-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        rel: 'android-chrome-512x512',
        url: '/android-chrome-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
  },
  manifest: '/site.webmanifest',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#ff1493',
  colorScheme: 'dark light',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="antialiased">
      <body>
        {/* Floating decorative elements */}
        <div className="floating-elements">
          <div className="floating-element text-4xl">💖</div>
          <div className="floating-element text-3xl">⭐</div>
          <div className="floating-element text-2xl">🌟</div>
          <div className="floating-element text-3xl">💜</div>
          <div className="floating-element text-2xl">✨</div>
          <div className="floating-element text-4xl">🦄</div>
        </div>
        <ShopifyProviders>
          {children}
        </ShopifyProviders>
      </body>
    </html>
  );
}
